import { type RefObject } from "react";

import { ContentLoading } from "@/components/ui/ContentLoading";

import { usePortalStaffEdit } from "../../hooks/usePortalStaffEdit";

import { PortalStaffEditForm } from "./PortalStaffEditForm";

type Props = {
  onTogglePreview?: () => void;
  onPreview?: (data: unknown) => void;
};

export const PortalStaffEdit: React.FC<Props> = ({
  onTogglePreview,
  onPreview,
}) => {
  const { loading, staffInfo, hospital } = usePortalStaffEdit();

  if (loading) {
    return <ContentLoading />;
  }

  if (
    staffInfo === null ||
    typeof staffInfo === "undefined" ||
    hospital === null ||
    typeof hospital === "undefined"
  ) {
    return null;
  }

  return (
    <PortalStaffEditForm
      staffInfo={staffInfo}
      isActive={hospital.isActive}
      onTogglePreview={onTogglePreview}
      onPreview={onPreview}
      hospital={hospital}
    />
  );
};
